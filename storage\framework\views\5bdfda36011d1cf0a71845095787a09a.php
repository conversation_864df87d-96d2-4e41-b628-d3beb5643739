

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'nta_parent_department' => null,
    'nta_department' => null,
    'nta_tashkil' => null,
    'nta_employee_type' => null,
    'nta_grid' => null,
    'nta_step' => null,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'nta_parent_department' => null,
    'nta_department' => null,
    'nta_tashkil' => null,
    'nta_employee_type' => null,
    'nta_grid' => null,
    'nta_step' => null,
]); ?>
<?php foreach (array_filter(([
    'nta_parent_department' => null,
    'nta_department' => null,
    'nta_tashkil' => null,
    'nta_employee_type' => null,
    'nta_grid' => null,
    'nta_step' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<table class="table table-bordered" id="bast_info_table">
    <thead style="background-color: #fff9e5;">
        <tr>
            <th colspan="2" style="font-size: 18px;">
                <?php echo e(__('general.bast_specifications')); ?>

            </th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.year')); ?>

            </td>
            <td id="bast_tashkil_year_cell">
                <?php echo e(getCurrentShamsiYear()); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.parent_department')); ?>

            </td>
            <td id="bast_parent_department_td">
                <?php echo e($nta_parent_department->name); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.department')); ?>

            </td>
            <td id="bast_department_td">
                <?php echo e($nta_department->name); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.bast_title')); ?>

            </td>
            <td id="bast_title_td">
                <?php echo e($nta_tashkil->title); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.nta_grid')); ?>

            </td>
            <td id="grid_td">
                <?php echo e($nta_grid->name); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.nta_step')); ?>

            </td>
            <td id="step_td">
                <?php echo e($nta_step->name); ?>

            </td>
        </tr>
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.employee_type')); ?>

            </td>
            <td id="bast_employee_type_td">
                <?php echo e($nta_employee_type->name); ?>

            </td>
        </tr>
        
        <tr>
            <td class="fixed_width">
                <?php echo e(__('general.employeed_type')); ?>

            </td>
            <td id="bast_employeed_type_td">
                <?php echo e(__('general.newly_hired')); ?>

            </td>
        </tr>
    </tbody>
</table>
<?php /**PATH C:\wamp64\www\newHR\resources\views/components/pages/recruitment/contract_employees/nta_bast_specification_edit.blade.php ENDPATH**/ ?>