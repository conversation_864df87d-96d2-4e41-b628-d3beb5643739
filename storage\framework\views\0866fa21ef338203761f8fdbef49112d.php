<?php $__env->startPush('plainCSS'); ?>
    <style>
        .user_att {
            width: 70% !important;
            min-height: 200px !important;

        }

        .user_att div {
            transition: ease 0.5s all;
            border-radius: 0.5rem;
        }

        .user_att div:hover {
            background-color: #f4f4f4;
            box-shadow: 0 0.125rem 0.25rem 0 rgba(133, 146, 163, 0.4) !important;
            color: #696cff;
        }

        .user_att div:hover a {
            color: #696cff !important;
        }
    </style>
<?php $__env->stopPush(); ?>
<div class="row">
    <div class="col-lg-8 mb-4 order-0">
        <div class="row">
            <div class="col-sm-12 mb-4">
                <div class="d-flex h-100 rounded  flex-column gap-1">
                    <?php if (isset($component)) { $__componentOriginal58a71839d0a4344691af4aacad614288 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal58a71839d0a4344691af4aacad614288 = $attributes; } ?>
<?php $component = App\View\Components\Pagecontents\AttendanceannouncementBar::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pagecontents.attendanceannouncement-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Pagecontents\AttendanceannouncementBar::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal58a71839d0a4344691af4aacad614288)): ?>
<?php $attributes = $__attributesOriginal58a71839d0a4344691af4aacad614288; ?>
<?php unset($__attributesOriginal58a71839d0a4344691af4aacad614288); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal58a71839d0a4344691af4aacad614288)): ?>
<?php $component = $__componentOriginal58a71839d0a4344691af4aacad614288; ?>
<?php unset($__componentOriginal58a71839d0a4344691af4aacad614288); ?>
<?php endif; ?>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="card">
                    <div class="row row-bordered g-0">
                        <div class="col-md-12">
                            <div class="card h-100">
                                <?php if (isset($component)) { $__componentOriginal57ae065401b9f5e46be5eef326c6b750 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal57ae065401b9f5e46be5eef326c6b750 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pagecontents.forms','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pagecontents.forms'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal57ae065401b9f5e46be5eef326c6b750)): ?>
<?php $attributes = $__attributesOriginal57ae065401b9f5e46be5eef326c6b750; ?>
<?php unset($__attributesOriginal57ae065401b9f5e46be5eef326c6b750); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal57ae065401b9f5e46be5eef326c6b750)): ?>
<?php $component = $__componentOriginal57ae065401b9f5e46be5eef326c6b750; ?>
<?php unset($__componentOriginal57ae065401b9f5e46be5eef326c6b750); ?>
<?php endif; ?>
                            </div>
                        </div>
                       
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="col-lg-4 mb-4 order-1">
        <div class="card h-100">
            <div class="card-body">
                <?php if (isset($component)) { $__componentOriginalcb4671c35f71d67f94ef6dd3830402fb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcb4671c35f71d67f94ef6dd3830402fb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.libs.calendar.fa.calendar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('libs.calendar.fa.calendar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcb4671c35f71d67f94ef6dd3830402fb)): ?>
<?php $attributes = $__attributesOriginalcb4671c35f71d67f94ef6dd3830402fb; ?>
<?php unset($__attributesOriginalcb4671c35f71d67f94ef6dd3830402fb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcb4671c35f71d67f94ef6dd3830402fb)): ?>
<?php $component = $__componentOriginalcb4671c35f71d67f94ef6dd3830402fb; ?>
<?php unset($__componentOriginalcb4671c35f71d67f94ef6dd3830402fb); ?>
<?php endif; ?>

            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\wamp64\www\newHR\resources\views/components/pagecontents/att_user_content.blade.php ENDPATH**/ ?>