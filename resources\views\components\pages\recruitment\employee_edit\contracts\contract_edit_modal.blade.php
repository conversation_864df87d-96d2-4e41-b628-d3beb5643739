{{-- 
    NOTES:
    PAGE: resources/views/pages/recruitment/edit.blade.php
    PARENT COMPONENT: resources/views/components/pages/recruitment/employee_edit/contracts.blade.php
    DESCRIPTION: THIS COMPONENT IS USED TO UPDATE AN EMPLOYEE CONTRACT RECORD
    PATH: THIS IS THE CONTRACT UPDATE COMPONENT INSIDE contracts.blade.php COMPONENT INSIDE edit.blade.php PAGE
    NOTE: ALL THE FIELD NAMES HAVE [_edit] TO DIFFERENCIATE BETWEEN THIS FORM AND CREATE FORM
--}}

{{-- DATA PASSED TO THIS COMPONENT FROM contracts.blade.php COMPONENT --}}
@props(['data' => null])

{{-- START DECONSTRUCT DATA --}}
@php
    if (!is_null($data)) {
        $parent_departments = $data['parent_departments'];
        $parent_department = $data['parent_department'];
        $departments = isset($data['departments']) ? $data['departments'] : [];
        $department = $data['department'];
        $nta_tashkil = $data['nta_tashkil'];
        $employee_types = $data['employee_types'];
        $employee_type = $data['employee_type'];
        $contract = $data['contract'];
        $nta_grid = $data['nta_grid'];
        $nta_step = $data['nta_step'];
        $nta_tashkilat = $data['nta_tashkilat'];
        $reason = '';
        if (!is_null($contract)) {
            if ($contract->contract_status_id == 2) {
                $reason = $contract->suspensionReason->name;
            } elseif ($contract->contract_status_id == 3) {
                $reason = $contract->cancellationReason->name;
            }
            $start_date = dateTo($contract->start_date, 'shamsi', false);
            $end_date = dateTo($contract->end_date, 'shamsi', false);
            $hokm_date = dateTo($contract->contract_hokm_date, 'shamsi', false);
        }
    }

@endphp
{{-- END DECONSTRUCT DATA --}}

{{-- CHECK IF THE CONTRACT IS NOT EMPTY --}}
@if (!is_null($contract))
    {{-- MODAL HEADER --}}
    <div class="modal-header pb-4 border-bottom">
        <div class="modal-title" id="backDropModalTitle">
            <div class="d-flex">
                <h5>
                    <span>{{ __('general.edit_contract') }}</span>
                </h5>
                <div class="mx-2">
                    <x-pages.recruitment.employee_edit.contracts.contract_status_tag :status_id="$contract->contract_status_id" />
                </div>
                <div>
                    <x-pages.recruitment.employee_edit.contracts.contract_reason_tag :reason_name="$reason" />
                </div>
            </div>
        </div>

        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    {{-- START MODAL BODY --}}
    <div class="modal-body py-4">

        {{-- CONTRACT TYPE --}}
        {{-- <div class="col-3">
            <label class="form-label">{{ __('general.contract_type') }}</label>
            <div class="form-check">
                <input onclick="onChangeContractType(this.value, true)" name="contract_type_edit"
                    class="form-check-input" type="radio" value="1" id="contract_type_nta_edit"
                    {{ $contract->contract_type_id == 1 ? 'checked' : '' }}>
                <label class="form-check-label" for="contract_type_nta_edit">{{ __('general.nta') }}</label>
            </div>
            <div class="form-check">
                <input onclick="onChangeContractType(this.value, true)" name="contract_type_edit"
                    class="form-check-input" type="radio" value="2" id="contract_type_bilmaqta_edit"
                    {{ $contract->contract_type_id == 2 ? 'checked' : '' }}>
                <label class="form-check-label" for="contract_type_bilmaqta_edit">{{ __('general.bilmaqta') }}</label>
            </div>
            <span id="contract_type_error" class="my-error"></span>
        </div> --}}

        {{-- <br> --}}
        
        @if ($contract->contract_type_id == 1)
            {{-- start NTA --}}
            <div id="contract_nta_edit">
                <div class="row">
                    {{-- BAST SPECIFICATIONS --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4 mt-4">
                        <div class="row">
                            <div class="col-12">
                                <x-pages.recruitment.contract_employees.nta_bast_specification_edit :nta_parent_department="$parent_department"
                                    :nta_department="$department" :nta_tashkil="$nta_tashkil" :nta_employee_type="$employee_type" :nta_grid="$nta_grid"
                                    :nta_step="$nta_step" />
                            </div>
                        </div>
                    </div>

                    {{-- BAST SELECTION --}}
                    <div class="col-12 col-sm-12 col-md-8 col-lg-8">
                        <div class="row">
                            {{-- PARENT DEPARTMENT --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_parent_department_edit"
                                    class="form-label">{{ __('general.parent_department') }}</label>
                                <select name="contract_parent_department_edit" id="contract_parent_department_edit"
                                    class="form-select" required onchange="fetchNtaSubDepartment(this.value)">
                                    <option value="0">{{ __('general.select') }}</option>
                                    @foreach ($parent_departments as $dept)
                                        <option value="{{ encrypt($dept->id) }}"
                                            {{ $contract->parent_department_id == $dept->id ? 'selected' : '' }}>
                                            {{ $dept->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <span id="contract_parent_department_edit_error" class="my-error"></span>
                            </div>

                            {{-- SUB DEPARTMENT --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_department_edit"
                                    class="form-label">{{ __('general.department') }}</label>
                                <select name="contract_department_edit" id="contract_department_edit"
                                    class="form-select" required
                                    onchange="getDepartmentFreeNtaBastsForNewTainat(this.value)">
                                    <option value="0">{{ __('general.select') }}</option>
                                    @foreach ($departments as $dept)
                                        <option value="{{ encrypt($dept->id) }}"
                                            {{ $contract->department_id == $dept->id ? 'selected' : '' }}>
                                            {{ $dept->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <span id="contract_department_edit_error" class="my-error"></span>
                            </div>

                            {{-- CONTRACT JOB TITLE --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_job_title_edit"
                                    class="form-label">{{ __('general.job_title') }}</label>

                                <select name="contract_job_title_edit" id="contract_job_title_edit"
                                    class="form-select select2" required onchange="getTashkilatNtaBastById(this.value)">
                                    <option value="0">{{ __('general.select') }}</option>
                                    @foreach ($nta_tashkilat as $nta_tashkil_bast)
                                        @if (!is_null($nta_tashkil_bast))
                                            <option value="{{ encrypt($nta_tashkil_bast->id) }}"
                                                {{ $contract->nta_tashkil_id == $nta_tashkil_bast->id ? 'selected' : '' }}>
                                                {{ $nta_tashkil_bast->title }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                                <span id="contract_job_title_edit_error" class="my-error"></span>
                            </div>

                        </div>

                        <br>

                        <div class="row">

                            {{-- START CONTRACT DATE --}}
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                <label for="contract_start_date_edit"
                                    class="form-label">{{ __('general.start_date') }}</label>
                                <x-tools.utilities.datepicker.dariDatePicker name="contract_start_date_edit"
                                    withID="contract_start_date_edit" :value="$start_date"
                                    withPlaceHolder="{{ __('general.contract_start_date') }}" withSize="3"
                                    extraClasses="" />
                                <span id="contract_start_date_edit_error" class="my-error"></span>
                            </div>

                            {{-- END CONTRACT DATE --}}
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                <label for="contract_end_date_edit"
                                    class="form-label">{{ __('general.end_date') }}</label>
                                <x-tools.utilities.datepicker.dariDatePicker name="contract_end_date_edit"
                                    withID="contract_end_date_edit" :value="$end_date"
                                    withPlaceHolder="{{ __('general.contract_end_date') }}" withSize="3"
                                    extraClasses="" />
                                <span id="contract_end_date_edit_error" class="my-error"></span>
                            </div>
                        </div>

                        <br>

                        <div class="row">
                            {{-- CONTRACT HOKM DATE --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_date_edit"
                                    class="form-label">{{ __('general.hokm_date') }}</label>
                                <x-tools.utilities.datepicker.dariDatePicker name="contract_hokm_date_edit"
                                    withID="contract_hokm_date_edit" :value="$hokm_date"
                                    withPlaceHolder="{{ __('general.hokm_date') }}" withSize="3" />
                                <span id="contract_hokm_date_edit_error" class="my-error"></span>
                            </div>

                            {{-- CONTRACT HOKM NUMBER --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_number_edit"
                                    class="form-label">{{ __('general.hokm_number') }}</label>
                                <input name="contract_hokm_number_edit" id="contract_hokm_number_edit"
                                    class="form-control" type="number"
                                    value="{{ $contract->contract_hokm_number }}" required autocomplete="on"
                                    placeholder="{{ __('general.hokm_number') }}">
                                <span id="contract_hokm_number_edit_error" class="my-error"></span>
                            </div>

                            {{-- CONTRACT HOKM ATTACHMENT --}}
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_attachment_edit"
                                    class="form-label">{{ __('general.hokm_attachment') }}</label>
                                <input id="contract_hokm_attachment_edit" name="contract_hokm_attachment_edit"
                                    class="form-control" type="file" accept="image/*,application/pdf" />
                                <span id="contract_hokm_attachment_edit_error" class="my-error"></span>
                            </div>
                        </div>

                        <br>

                    </div>
                </div>
            </div>
            {{-- START JAVASCRIPT --}}
            <script>
                // fetch sub department
                async function fetchNtaSubDepartment(departmentId) {
                    try {
                        // remove all departments and tashkil basts
                        $('#contract_department_edit option[value!="0"]').remove();
                        $('#contract_job_title_edit option[value!="0"]').remove();

                        // clear bast spec details
                        if (departmentId == 0) {
                            $("#bast_parent_department_td").empty();
                        } else {
                            $("#bast_parent_department_td").text($("#contract_parent_department_edit option:selected").text());
                        }

                        $("#bast_department_td").empty();
                        $("#bast_title_td").empty();
                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        // check if parent department is selected
                        if (departmentId != null && departmentId != 0) {
                            // empty the sub departments dropdown
                            let subDepartment = $("#contract_department_edit");
                            subDepartment.empty();
                            let response = await axiosObj.get("{{ route('departments.rendered.get') }}", {
                                params: {
                                    type: 'child',
                                    parentID: departmentId
                                }
                            });

                            if (response != null && response.data != null) {
                                // append the fetched departments
                                $("#contract_department_edit").append(response.data);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                    }
                }

                // when department is changed, so get all the free bast from nta_tashkilat table
                // this function is called with parent department onchange event
                async function getDepartmentFreeNtaBastsForNewTainat(departmentId) {
                    try {
                        // empty the dropdown without the first [select] option
                        $('#contract_job_title_edit option[value!="0"]').remove();

                        // clear bast spec details
                        if (departmentId == 0) {
                            $("#bast_department_td").empty();
                        } else {
                            $("#bast_department_td").text($("#contract_department_edit option:selected").text());
                        }
                        $("#bast_title_td").empty();
                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        // check if department is selected
                        if (departmentId != null && departmentId != 0) {
                            let bast = $("#contract_job_title_edit");
                            bast.empty();

                            // send the request to server
                            let response = await axiosObj.get("{{ route('nta.tashkil.department.basts.free.rendered') }}", {
                                params: {
                                    id: departmentId
                                }
                            });

                            // check if response is not empty [response is the list of <option>]
                            if (response != null && response.data != null) {
                                // append the options to the dropdown
                                bast.append(response.data);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                    }
                }

                // get tashkil bast by id
                // this function is called with onchange event on sub department dropdown
                async function getTashkilatNtaBastById(id) {
                    try {

                        // clear bast spec details
                        if (id == 0) {
                            $("#bast_title_td").empty();
                        }

                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        if (id != null && id != 0) {
                            // get bast info
                            let response = await axiosObj.get(
                                "{{ route('nta.tashkil.bast') }}", {
                                    params: {
                                        id: id,
                                    }
                                });
                            if (response != null && response.data != null) {
                                // print the details to bast specifications table
                                $("#bast_title_td").text(response.data['title']);
                                $("#grid_td").text(response.data['grid']);
                                $("#step_td").text(response.data['step']);
                                $("#bast_employee_type_td").text(response.data['employee_type_name']);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                    }
                }
            </script>
            {{-- END JAVASCRIPT --}}

            {{-- end NTA --}}
        @elseif($contract->contract_type_id == 2)
            {{-- start bilmaqta --}}
            <div id="contract_bilmaqta_edit" class="d-none">
                <div class="row">
                    {{-- PARENT DEPARTMENT --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_parent_department_edit"
                            class="form-label">{{ __('general.parent_department') }}</label>
                        <select name="contract_parent_department_edit" id="contract_parent_department_edit"
                            class="form-select" required
                            onchange="getSubDepartments(this.value, 'contract_department_edit')">
                            <option value="0">{{ __('general.select') }}</option>
                            @foreach ($parent_departments as $dept)
                                <option value="{{ encrypt($dept->id) }}"
                                    {{ $contract->parent_department_id == $dept->id ? 'selected' : '' }}>
                                    {{ $dept->name }}
                                </option>
                            @endforeach
                        </select>
                        <span id="contract_parent_department_edit_error" class="my-error"></span>
                    </div>

                    {{-- SUB DEPARTMENT --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_department_edit"
                            class="form-label">{{ __('general.department') }}</label>
                        <select name="contract_department_edit" id="contract_department_edit" class="form-select"
                            required>
                            <option value="0">{{ __('general.select') }}</option>
                            @foreach ($departments as $dept)
                                <option value="{{ encrypt($dept->id) }}"
                                    {{ $contract->department_id == $dept->id ? 'selected' : '' }}>
                                    {{ $dept->name }}
                                </option>
                            @endforeach
                        </select>
                        <span id="contract_department_edit_error" class="my-error"></span>
                    </div>

                    {{-- EMPLOYEE TYPE --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_employee_type_edit"
                            class="form-label">{{ __('general.employee_type') }}</label>
                        <select name="contract_employee_type_edit" id="contract_employee_type_edit"
                            class="form-select" required>
                            @foreach ($employee_types as $type)
                                @if ($type->id == 1 || $type->id == 2 || $type->id == 3)
                                    <option value="{{ $type->id }}"
                                        {{ $contract->employee_type_id == $type->id ? 'selected' : '' }}>
                                        {{ $type->name }}
                                    </option>
                                @endif
                            @endforeach
                        </select>
                        <span id="contract_employee_type_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>

                <div class="row">
                    {{-- CONTRACT JOB TITLE --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_job_title_edit" class="form-label">{{ __('general.job_title') }}</label>
                        <input name="contract_job_title_edit" id="contract_job_title_edit" class="form-control"
                            type="text" value="{{ $contract->job_title }}" required autocomplete="off"
                            placeholder="{{ __('general.job_title') }}">
                        <span id="contract_job_title_edit_error" class="my-error"></span>
                    </div>

                    {{-- START CONTRACT DATE --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_start_date_edit"
                            class="form-label">{{ __('general.start_date') }}</label>
                        <x-tools.utilities.datepicker.dariDatePicker name="contract_start_date_edit"
                            withID="contract_start_date_edit" :value="$start_date"
                            withPlaceHolder="{{ __('general.contract_start_date') }}" withSize="3"
                            extraClasses="" />
                        <span id="contract_start_date_edit_error" class="my-error"></span>
                    </div>

                    {{-- END CONTRACT DATE --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_end_date_edit" class="form-label">{{ __('general.end_date') }}</label>
                        <x-tools.utilities.datepicker.dariDatePicker name="contract_end_date_edit"
                            withID="contract_end_date_edit" :value="$end_date"
                            withPlaceHolder="{{ __('general.contract_end_date') }}" withSize="3"
                            extraClasses="" />
                        <span id="contract_end_date_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>

                <div class="row">
                    {{-- CONTRACT HOKM DATE --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_date_edit" class="form-label">{{ __('general.hokm_date') }}</label>
                        <x-tools.utilities.datepicker.dariDatePicker name="contract_hokm_date_edit"
                            withID="contract_hokm_date_edit" :value="$hokm_date"
                            withPlaceHolder="{{ __('general.hokm_date') }}" withSize="3" />
                        <span id="contract_hokm_date_edit_error" class="my-error"></span>
                    </div>

                    {{-- CONTRACT HOKM NUMBER --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_number_edit"
                            class="form-label">{{ __('general.hokm_number') }}</label>
                        <input name="contract_hokm_number_edit" id="contract_hokm_number_edit" class="form-control"
                            type="number" value="{{ $contract->contract_hokm_number }}" required autocomplete="on"
                            placeholder="{{ __('general.hokm_number') }}">
                        <span id="contract_hokm_number_edit_error" class="my-error"></span>
                    </div>

                    {{-- CONTRACT HOKM ATTACHMENT --}}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_attachment_edit"
                            class="form-label">{{ __('general.hokm_attachment') }}</label>
                        <input id="contract_hokm_attachment_edit" name="contract_hokm_attachment_edit"
                            class="form-control" type="file" accept="image/*,application/pdf" />
                        <span id="contract_hokm_attachment_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>
            </div>
            {{-- end bilmaqta --}}
        @endif
    </div>
    {{-- END MODAL BODY --}}

    {{-- START MODAL FOOTER --}}
    <div class="modal-footer pt-4 border-top">
        <button type="button" class="btn btn-sm btn-label-secondary" data-bs-dismiss="modal">
            {{ __('general.close') }}
        </button>
        @if (!empty($contract->attachment_url))
            <button type="button" class="btn btn-sm btn-warning"
                data-target="confirm_delete_contract_attachment_modal" data-bs-toggle="modal"
                data-bs-target="#confirm_delete_contract_attachment_modal">
                {{ __('general.delete_attachment') }}
            </button>
        @endif
        <button onclick="updateContract()" type="button" class="btn btn-sm btn-primary has-spinner"
            id="update_contract_btn">
            {{ __('general.save') }}
        </button>
    </div>
    {{-- END MODAL FOOTER --}}
@endif
