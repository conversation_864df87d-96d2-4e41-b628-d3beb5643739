


<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['data' => null]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['data' => null]); ?>
<?php foreach (array_filter((['data' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>


<?php
    if (!is_null($data)) {
        $parent_departments = $data['parent_departments'];
        $parent_department = $data['parent_department'];
        $departments = isset($data['departments']) ? $data['departments'] : [];
        $department = $data['department'];
        $nta_tashkil = $data['nta_tashkil'];
        $employee_types = $data['employee_types'];
        $employee_type = $data['employee_type'];
        $contract = $data['contract'];
        $nta_grid = $data['nta_grid'];
        $nta_step = $data['nta_step'];
        $nta_tashkilat = $data['nta_tashkilat'];
        $reason = '';
        if (!is_null($contract)) {
            if ($contract->contract_status_id == 2) {
                $reason = $contract->suspensionReason->name;
            } elseif ($contract->contract_status_id == 3) {
                $reason = $contract->cancellationReason->name;
            }
            $start_date = dateTo($contract->start_date, 'shamsi', false);
            $end_date = dateTo($contract->end_date, 'shamsi', false);
            $hokm_date = dateTo($contract->contract_hokm_date, 'shamsi', false);
        }
    }

?>



<?php if(!is_null($contract)): ?>
    
    <div class="modal-header pb-4 border-bottom">
        <div class="modal-title" id="backDropModalTitle">
            <div class="d-flex">
                <h5>
                    <span><?php echo e(__('general.edit_contract')); ?></span>
                </h5>
                <div class="mx-2">
                    <?php if (isset($component)) { $__componentOriginal1b6c3627a401a9d4881e8d50dffe9622 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1b6c3627a401a9d4881e8d50dffe9622 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pages.recruitment.employee_edit.contracts.contract_status_tag','data' => ['statusId' => $contract->contract_status_id]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pages.recruitment.employee_edit.contracts.contract_status_tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status_id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($contract->contract_status_id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1b6c3627a401a9d4881e8d50dffe9622)): ?>
<?php $attributes = $__attributesOriginal1b6c3627a401a9d4881e8d50dffe9622; ?>
<?php unset($__attributesOriginal1b6c3627a401a9d4881e8d50dffe9622); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1b6c3627a401a9d4881e8d50dffe9622)): ?>
<?php $component = $__componentOriginal1b6c3627a401a9d4881e8d50dffe9622; ?>
<?php unset($__componentOriginal1b6c3627a401a9d4881e8d50dffe9622); ?>
<?php endif; ?>
                </div>
                <div>
                    <?php if (isset($component)) { $__componentOriginalb79f70267220a0484b9f242b2ff154e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb79f70267220a0484b9f242b2ff154e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pages.recruitment.employee_edit.contracts.contract_reason_tag','data' => ['reasonName' => $reason]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pages.recruitment.employee_edit.contracts.contract_reason_tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['reason_name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reason)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb79f70267220a0484b9f242b2ff154e4)): ?>
<?php $attributes = $__attributesOriginalb79f70267220a0484b9f242b2ff154e4; ?>
<?php unset($__attributesOriginalb79f70267220a0484b9f242b2ff154e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb79f70267220a0484b9f242b2ff154e4)): ?>
<?php $component = $__componentOriginalb79f70267220a0484b9f242b2ff154e4; ?>
<?php unset($__componentOriginalb79f70267220a0484b9f242b2ff154e4); ?>
<?php endif; ?>
                </div>
            </div>
        </div>

        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    
    <div class="modal-body py-4">

        
        

        <br>
        <?php if($contract->contract_type_id == 1): ?>
            
            <div id="contract_nta_edit">
                <div class="row">
                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4 mt-4">
                        <div class="row">
                            <div class="col-12">
                                <?php if (isset($component)) { $__componentOriginal4fa18a3755b89708039b5c3e6ff4daf8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fa18a3755b89708039b5c3e6ff4daf8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pages.recruitment.contract_employees.nta_bast_specification_edit','data' => ['ntaParentDepartment' => $parent_department,'ntaDepartment' => $department,'ntaTashkil' => $nta_tashkil,'ntaEmployeeType' => $employee_type,'ntaGrid' => $nta_grid,'ntaStep' => $nta_step]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pages.recruitment.contract_employees.nta_bast_specification_edit'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nta_parent_department' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($parent_department),'nta_department' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($department),'nta_tashkil' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($nta_tashkil),'nta_employee_type' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($employee_type),'nta_grid' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($nta_grid),'nta_step' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($nta_step)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fa18a3755b89708039b5c3e6ff4daf8)): ?>
<?php $attributes = $__attributesOriginal4fa18a3755b89708039b5c3e6ff4daf8; ?>
<?php unset($__attributesOriginal4fa18a3755b89708039b5c3e6ff4daf8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fa18a3755b89708039b5c3e6ff4daf8)): ?>
<?php $component = $__componentOriginal4fa18a3755b89708039b5c3e6ff4daf8; ?>
<?php unset($__componentOriginal4fa18a3755b89708039b5c3e6ff4daf8); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-8 col-lg-8">
                        <div class="row">
                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_parent_department_edit"
                                    class="form-label"><?php echo e(__('general.parent_department')); ?></label>
                                <select name="contract_parent_department_edit" id="contract_parent_department_edit"
                                    class="form-select" required onchange="fetchNtaSubDepartment(this.value)">
                                    <option value="0"><?php echo e(__('general.select')); ?></option>
                                    <?php $__currentLoopData = $parent_departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e(encrypt($dept->id)); ?>"
                                            <?php echo e($contract->parent_department_id == $dept->id ? 'selected' : ''); ?>>
                                            <?php echo e($dept->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <span id="contract_parent_department_edit_error" class="my-error"></span>
                            </div>

                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_department_edit"
                                    class="form-label"><?php echo e(__('general.department')); ?></label>
                                <select name="contract_department_edit" id="contract_department_edit"
                                    class="form-select" required
                                    onchange="getDepartmentFreeNtaBastsForNewTainat(this.value)">
                                    <option value="0"><?php echo e(__('general.select')); ?></option>
                                    <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e(encrypt($dept->id)); ?>"
                                            <?php echo e($contract->department_id == $dept->id ? 'selected' : ''); ?>>
                                            <?php echo e($dept->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <span id="contract_department_edit_error" class="my-error"></span>
                            </div>

                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_job_title_edit"
                                    class="form-label"><?php echo e(__('general.job_title')); ?></label>

                                <select name="contract_job_title_edit" id="contract_job_title_edit"
                                    class="form-select select2" required onchange="getTashkilatNtaBastById(this.value)">
                                    <option value="0"><?php echo e(__('general.select')); ?></option>
                                    <?php $__currentLoopData = $nta_tashkilat; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nta_tashkil_bast): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(!is_null($nta_tashkil_bast)): ?>
                                            <option value="<?php echo e(encrypt($nta_tashkil_bast->id)); ?>"
                                                <?php echo e($contract->nta_tashkil_id == $nta_tashkil_bast->id ? 'selected' : ''); ?>>
                                                <?php echo e($nta_tashkil_bast->title); ?>

                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <span id="contract_job_title_edit_error" class="my-error"></span>
                            </div>

                        </div>

                        <br>

                        <div class="row">

                            
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                <label for="contract_start_date_edit"
                                    class="form-label"><?php echo e(__('general.start_date')); ?></label>
                                <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_start_date_edit','withID' => 'contract_start_date_edit','value' => $start_date,'withPlaceHolder' => ''.e(__('general.contract_start_date')).'','withSize' => '3','extraClasses' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_start_date_edit','withID' => 'contract_start_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($start_date),'withPlaceHolder' => ''.e(__('general.contract_start_date')).'','withSize' => '3','extraClasses' => '']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                                <span id="contract_start_date_edit_error" class="my-error"></span>
                            </div>

                            
                            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                                <label for="contract_end_date_edit"
                                    class="form-label"><?php echo e(__('general.end_date')); ?></label>
                                <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_end_date_edit','withID' => 'contract_end_date_edit','value' => $end_date,'withPlaceHolder' => ''.e(__('general.contract_end_date')).'','withSize' => '3','extraClasses' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_end_date_edit','withID' => 'contract_end_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($end_date),'withPlaceHolder' => ''.e(__('general.contract_end_date')).'','withSize' => '3','extraClasses' => '']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                                <span id="contract_end_date_edit_error" class="my-error"></span>
                            </div>
                        </div>

                        <br>

                        <div class="row">
                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_date_edit"
                                    class="form-label"><?php echo e(__('general.hokm_date')); ?></label>
                                <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_hokm_date_edit','withID' => 'contract_hokm_date_edit','value' => $hokm_date,'withPlaceHolder' => ''.e(__('general.hokm_date')).'','withSize' => '3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_hokm_date_edit','withID' => 'contract_hokm_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hokm_date),'withPlaceHolder' => ''.e(__('general.hokm_date')).'','withSize' => '3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                                <span id="contract_hokm_date_edit_error" class="my-error"></span>
                            </div>

                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_number_edit"
                                    class="form-label"><?php echo e(__('general.hokm_number')); ?></label>
                                <input name="contract_hokm_number_edit" id="contract_hokm_number_edit"
                                    class="form-control" type="number"
                                    value="<?php echo e($contract->contract_hokm_number); ?>" required autocomplete="on"
                                    placeholder="<?php echo e(__('general.hokm_number')); ?>">
                                <span id="contract_hokm_number_edit_error" class="my-error"></span>
                            </div>

                            
                            <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                <label for="contract_hokm_attachment_edit"
                                    class="form-label"><?php echo e(__('general.hokm_attachment')); ?></label>
                                <input id="contract_hokm_attachment_edit" name="contract_hokm_attachment_edit"
                                    class="form-control" type="file" accept="image/*,application/pdf" />
                                <span id="contract_hokm_attachment_edit_error" class="my-error"></span>
                            </div>
                        </div>

                        <br>

                    </div>
                </div>
            </div>
            
            <script>
                // fetch sub department
                async function fetchNtaSubDepartment(departmentId) {
                    try {
                        // remove all departments and tashkil basts
                        $('#contract_department_edit option[value!="0"]').remove();
                        $('#contract_job_title_edit option[value!="0"]').remove();

                        // clear bast spec details
                        if (departmentId == 0) {
                            $("#bast_parent_department_td").empty();
                        } else {
                            $("#bast_parent_department_td").text($("#contract_parent_department_edit option:selected").text());
                        }

                        $("#bast_department_td").empty();
                        $("#bast_title_td").empty();
                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        // check if parent department is selected
                        if (departmentId != null && departmentId != 0) {
                            // empty the sub departments dropdown
                            let subDepartment = $("#contract_department_edit");
                            subDepartment.empty();
                            let response = await axiosObj.get("<?php echo e(route('departments.rendered.get')); ?>", {
                                params: {
                                    type: 'child',
                                    parentID: departmentId
                                }
                            });

                            if (response != null && response.data != null) {
                                // append the fetched departments
                                $("#contract_department_edit").append(response.data);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("<?php echo e(trans('general.something_wrong_happened')); ?>", false);
                    }
                }

                // when department is changed, so get all the free bast from nta_tashkilat table
                // this function is called with parent department onchange event
                async function getDepartmentFreeNtaBastsForNewTainat(departmentId) {
                    try {
                        // empty the dropdown without the first [select] option
                        $('#contract_job_title_edit option[value!="0"]').remove();

                        // clear bast spec details
                        if (departmentId == 0) {
                            $("#bast_department_td").empty();
                        } else {
                            $("#bast_department_td").text($("#contract_department_edit option:selected").text());
                        }
                        $("#bast_title_td").empty();
                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        // check if department is selected
                        if (departmentId != null && departmentId != 0) {
                            let bast = $("#contract_job_title_edit");
                            bast.empty();

                            // send the request to server
                            let response = await axiosObj.get("<?php echo e(route('nta.tashkil.department.basts.free.rendered')); ?>", {
                                params: {
                                    id: departmentId
                                }
                            });

                            // check if response is not empty [response is the list of <option>]
                            if (response != null && response.data != null) {
                                // append the options to the dropdown
                                bast.append(response.data);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("<?php echo e(trans('general.something_wrong_happened')); ?>", false);
                    }
                }

                // get tashkil bast by id
                // this function is called with onchange event on sub department dropdown
                async function getTashkilatNtaBastById(id) {
                    try {

                        // clear bast spec details
                        if (id == 0) {
                            $("#bast_title_td").empty();
                        }

                        $("#grid_td").empty();
                        $("#step_td").empty();
                        $("#bast_employee_type_td").empty();

                        if (id != null && id != 0) {
                            // get bast info
                            let response = await axiosObj.get(
                                "<?php echo e(route('nta.tashkil.bast')); ?>", {
                                    params: {
                                        id: id,
                                    }
                                });
                            if (response != null && response.data != null) {
                                // print the details to bast specifications table
                                $("#bast_title_td").text(response.data['title']);
                                $("#grid_td").text(response.data['grid']);
                                $("#step_td").text(response.data['step']);
                                $("#bast_employee_type_td").text(response.data['employee_type_name']);
                            }
                        }

                    } catch (error) {
                        DisplayMessage("<?php echo e(trans('general.something_wrong_happened')); ?>", false);
                    }
                }
            </script>
            

            
        <?php elseif($contract->contract_type_id == 2): ?>
            
            <div id="contract_bilmaqta_edit" class="d-none">
                <div class="row">
                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_parent_department_edit"
                            class="form-label"><?php echo e(__('general.parent_department')); ?></label>
                        <select name="contract_parent_department_edit" id="contract_parent_department_edit"
                            class="form-select" required
                            onchange="getSubDepartments(this.value, 'contract_department_edit')">
                            <option value="0"><?php echo e(__('general.select')); ?></option>
                            <?php $__currentLoopData = $parent_departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e(encrypt($dept->id)); ?>"
                                    <?php echo e($contract->parent_department_id == $dept->id ? 'selected' : ''); ?>>
                                    <?php echo e($dept->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <span id="contract_parent_department_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_department_edit"
                            class="form-label"><?php echo e(__('general.department')); ?></label>
                        <select name="contract_department_edit" id="contract_department_edit" class="form-select"
                            required>
                            <option value="0"><?php echo e(__('general.select')); ?></option>
                            <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e(encrypt($dept->id)); ?>"
                                    <?php echo e($contract->department_id == $dept->id ? 'selected' : ''); ?>>
                                    <?php echo e($dept->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <span id="contract_department_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_employee_type_edit"
                            class="form-label"><?php echo e(__('general.employee_type')); ?></label>
                        <select name="contract_employee_type_edit" id="contract_employee_type_edit"
                            class="form-select" required>
                            <?php $__currentLoopData = $employee_types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($type->id == 1 || $type->id == 2 || $type->id == 3): ?>
                                    <option value="<?php echo e($type->id); ?>"
                                        <?php echo e($contract->employee_type_id == $type->id ? 'selected' : ''); ?>>
                                        <?php echo e($type->name); ?>

                                    </option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <span id="contract_employee_type_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>

                <div class="row">
                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_job_title_edit" class="form-label"><?php echo e(__('general.job_title')); ?></label>
                        <input name="contract_job_title_edit" id="contract_job_title_edit" class="form-control"
                            type="text" value="<?php echo e($contract->job_title); ?>" required autocomplete="off"
                            placeholder="<?php echo e(__('general.job_title')); ?>">
                        <span id="contract_job_title_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_start_date_edit"
                            class="form-label"><?php echo e(__('general.start_date')); ?></label>
                        <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_start_date_edit','withID' => 'contract_start_date_edit','value' => $start_date,'withPlaceHolder' => ''.e(__('general.contract_start_date')).'','withSize' => '3','extraClasses' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_start_date_edit','withID' => 'contract_start_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($start_date),'withPlaceHolder' => ''.e(__('general.contract_start_date')).'','withSize' => '3','extraClasses' => '']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                        <span id="contract_start_date_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_end_date_edit" class="form-label"><?php echo e(__('general.end_date')); ?></label>
                        <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_end_date_edit','withID' => 'contract_end_date_edit','value' => $end_date,'withPlaceHolder' => ''.e(__('general.contract_end_date')).'','withSize' => '3','extraClasses' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_end_date_edit','withID' => 'contract_end_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($end_date),'withPlaceHolder' => ''.e(__('general.contract_end_date')).'','withSize' => '3','extraClasses' => '']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                        <span id="contract_end_date_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>

                <div class="row">
                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_date_edit" class="form-label"><?php echo e(__('general.hokm_date')); ?></label>
                        <?php if (isset($component)) { $__componentOriginale05b40c22f966e63f8da6a734f575ad5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale05b40c22f966e63f8da6a734f575ad5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tools.utilities.datepicker.dariDatePicker','data' => ['name' => 'contract_hokm_date_edit','withID' => 'contract_hokm_date_edit','value' => $hokm_date,'withPlaceHolder' => ''.e(__('general.hokm_date')).'','withSize' => '3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('tools.utilities.datepicker.dariDatePicker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'contract_hokm_date_edit','withID' => 'contract_hokm_date_edit','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hokm_date),'withPlaceHolder' => ''.e(__('general.hokm_date')).'','withSize' => '3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $attributes = $__attributesOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__attributesOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale05b40c22f966e63f8da6a734f575ad5)): ?>
<?php $component = $__componentOriginale05b40c22f966e63f8da6a734f575ad5; ?>
<?php unset($__componentOriginale05b40c22f966e63f8da6a734f575ad5); ?>
<?php endif; ?>
                        <span id="contract_hokm_date_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_number_edit"
                            class="form-label"><?php echo e(__('general.hokm_number')); ?></label>
                        <input name="contract_hokm_number_edit" id="contract_hokm_number_edit" class="form-control"
                            type="number" value="<?php echo e($contract->contract_hokm_number); ?>" required autocomplete="on"
                            placeholder="<?php echo e(__('general.hokm_number')); ?>">
                        <span id="contract_hokm_number_edit_error" class="my-error"></span>
                    </div>

                    
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <label for="contract_hokm_attachment_edit"
                            class="form-label"><?php echo e(__('general.hokm_attachment')); ?></label>
                        <input id="contract_hokm_attachment_edit" name="contract_hokm_attachment_edit"
                            class="form-control" type="file" accept="image/*,application/pdf" />
                        <span id="contract_hokm_attachment_edit_error" class="my-error"></span>
                    </div>
                </div>

                <br>
            </div>
            
        <?php endif; ?>
    </div>
    

    
    <div class="modal-footer pt-4 border-top">
        <button type="button" class="btn btn-sm btn-label-secondary" data-bs-dismiss="modal">
            <?php echo e(__('general.close')); ?>

        </button>
        <?php if(!empty($contract->attachment_url)): ?>
            <button type="button" class="btn btn-sm btn-warning"
                data-target="confirm_delete_contract_attachment_modal" data-bs-toggle="modal"
                data-bs-target="#confirm_delete_contract_attachment_modal">
                <?php echo e(__('general.delete_attachment')); ?>

            </button>
        <?php endif; ?>
        <button onclick="updateContract()" type="button" class="btn btn-sm btn-primary has-spinner"
            id="update_contract_btn">
            <?php echo e(__('general.save')); ?>

        </button>
    </div>
    
<?php endif; ?>
<?php /**PATH C:\wamp64\www\newHR\resources\views/components/pages/recruitment/employee_edit/contracts/contract_edit_modal.blade.php ENDPATH**/ ?>