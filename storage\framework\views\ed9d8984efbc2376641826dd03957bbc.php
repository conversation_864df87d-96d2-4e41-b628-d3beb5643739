<?php if (isset($component)) { $__componentOriginal09ae9edb6a4f3743836239ca1a9c4719 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal09ae9edb6a4f3743836239ca1a9c4719 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.master','data' => ['title' => 'Home']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.master'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Home']); ?>

    <?php $__env->startPush('cssLinks'); ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/calendar/fa/calendar-fa.css')); ?>" />
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/dashboard.css')); ?>" />
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('content'); ?>
        <?php if (isset($component)) { $__componentOriginal80a797af8e4d8c08001ee2b2f44e91b2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal80a797af8e4d8c08001ee2b2f44e91b2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pagecontents.att_user_content','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('pagecontents.att_user_content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal80a797af8e4d8c08001ee2b2f44e91b2)): ?>
<?php $attributes = $__attributesOriginal80a797af8e4d8c08001ee2b2f44e91b2; ?>
<?php unset($__attributesOriginal80a797af8e4d8c08001ee2b2f44e91b2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80a797af8e4d8c08001ee2b2f44e91b2)): ?>
<?php $component = $__componentOriginal80a797af8e4d8c08001ee2b2f44e91b2; ?>
<?php unset($__componentOriginal80a797af8e4d8c08001ee2b2f44e91b2); ?>
<?php endif; ?>
    <?php $__env->stopPush(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal09ae9edb6a4f3743836239ca1a9c4719)): ?>
<?php $attributes = $__attributesOriginal09ae9edb6a4f3743836239ca1a9c4719; ?>
<?php unset($__attributesOriginal09ae9edb6a4f3743836239ca1a9c4719); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal09ae9edb6a4f3743836239ca1a9c4719)): ?>
<?php $component = $__componentOriginal09ae9edb6a4f3743836239ca1a9c4719; ?>
<?php unset($__componentOriginal09ae9edb6a4f3743836239ca1a9c4719); ?>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\newHR\resources\views/att_user_home.blade.php ENDPATH**/ ?>