<?php

namespace App\Http\Controllers\Recruitment;

use App\Exports\Recruitment\Contract\BilmaqtaContractEmployeesExcelReport;
use App\Exports\Recruitment\Contract\NTAContractEmployeesExcelReport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Recruitment\Contract\EmployeeContractAttachmentDeleteRequest;
use App\Http\Requests\Recruitment\Contract\EmployeeContractStatusUpdateRequest;
use App\Http\Requests\Recruitment\Contract\EmployeeContractStoreRequest;
use App\Http\Requests\Recruitment\Contract\EmployeeContractUpdateRequest;
use App\Models\MasterData\NTAGrid;
use App\Models\MasterData\NTAStep;
use App\Models\Recruitment\EmployeeType;
use App\Repositories\Recruitment\EmployeeAttachmentRepository;
use App\Repositories\Recruitment\EmployeeContractRepository;
use App\Repositories\Recruitment\EmployeeRepository;
use App\Repositories\Tashkilat\Department\DepartmentRepository;
use App\Repositories\Tashkilat\Tashkilat\NtaTashkilatRepository;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Log;

/**
 * This controller is used to manage the employee contracts records
 * This controller is also used to manage the contract employees [list of all employees that are contractor]
 */
class EmployeeContractController extends Controller
{
    use ResponseTrait;

    // repositories
    private $employeeContractRepository;
    private $employeeRepository;
    private $departmentRepository;
    private $employeeAttachmentRepository;
    private $ntaTashkilatRepository;

    // constructor
    public function __construct(EmployeeContractRepository $employeeContractRepository, EmployeeRepository $employeeRepository, DepartmentRepository $departmentRepository, EmployeeAttachmentRepository $employeeAttachmentRepository, NtaTashkilatRepository $ntaTashkilatRepository)
    {
        $this->middleware('auth');
        $this->employeeContractRepository = $employeeContractRepository;
        $this->employeeRepository = $employeeRepository;
        $this->departmentRepository = $departmentRepository;
        $this->employeeAttachmentRepository = $employeeAttachmentRepository;
        $this->ntaTashkilatRepository = $ntaTashkilatRepository;
    }

    /**
     * Get employees list that are contractor [کارمندان قرار دادی]
     * This function returns only the requested amount of records [10, 25, 50...]
     * @param request contains perPage param
     */
    public function getContractEmployees(Request $request)
    {
        try {
            if (canDo('recruitment-contractor-emp_show_contractor')) {
                $perPage = $request->input('perPage', 10);

                // get data from repository
                $data = $this->employeeContractRepository->getContractEmployees($perPage);

                // returns the page
                return view('pages.recruitment.contract_employees', ['data' => $data, 'pager' => withPaginate(['searchRouteName' => 'recruitment.employees.contract.search'])]);
            }
            return notAllowed();
            //code...
        } catch (\Exception $th) {
            Log::error($th->getMessage());
            dd($th);
            //throw $th;
        }
        try {
            // per page

        } catch (\Exception $ex) {
            return redirect()->back()->with('error', trans('general.fetched_fail'), $ex);
        }
    }

    /**
     * Search employees that are contrators [جستجوی کارمندان قرار دادی]
     * This function returns only the requests amount of records [10, 25, 50...]
     * @param  request contains perPage param
     */
    public function searchContractEmployees(Request $request)
    {
        try {
            // get data from repository
            $data = $this->employeeContractRepository->searchContractEmployees($request);

            // pass the data to datatable and return it
            return view('components.pages.recruitment.contract_employees.datatable', ['data' => $data]);
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('general.searched_fail'), $ex);
        }
    }

    /**
     * Search contract employees and export it to excel file
     * @param  request contains search params
     */
    public function searchContractEmployeesForExcel(Request $request)
    {
        try {
            if (canDo('recruitment-contractor-emp_excel_reports')) {
                // get data from employee repository
                $data = $this->employeeContractRepository->searchContractEmployeesForExcel($request);

                // file name and excel file based on contract type
                if ($request->contract_type == 1) { // NTA
                    $file_name = trans('recruitment/recruitment.nta_contractor_employees_export_to_excel_file_title', ['date' => getCurrentShamsiDate()]);
                    $excel = new NTAContractEmployeesExcelReport($data);
                } else if ($request->contract_type == 2) { // Bilmaqta
                    $file_name = trans('recruitment/recruitment.bilmaqta_contractor_employees_export_to_excel_file_title', ['date' => getCurrentShamsiDate()]);
                    $excel = new BilmaqtaContractEmployeesExcelReport($data);
                }
                // store one copy of the excel file to default storage in server
                Excel::store($excel, 'recruitment/exports/contract/' . $file_name . '_' . auth()->user()->id . '_' . time() . '.xlsx');

                // send one copy of excel file to user requested the report
                return Excel::download($excel, $file_name . '.xlsx');
            }
            return notAllowed();
            //code...
        } catch (\Exception $th) {
            log::error($th->getMessage());
            dd($th);
            //throw $th;
        }
    }

    /**
     * Get all NTA, Bilmaqta contractor employees count (Mamor, Ajir, Military)
     */
    public function getContractorsCount(Request $request)
    {
        try {
            // 1: NTA, 2: Bilmaqta
            $contract_type = $request->contract_type;

            // get count
            $all_count = $this->employeeContractRepository->getContractorsCount($contract_type, [1, 2, 3]);
            $employees_count = $this->employeeContractRepository->getContractorsCount($contract_type, [1]);
            $ajirs_count = $this->employeeContractRepository->getContractorsCount($contract_type, [2]);
            $militaries_count = $this->employeeContractRepository->getContractorsCount($contract_type, [3]);

            // success message based on contract type
            if ($request->contract_type == 1) {
                $message = 'fetched_nta_contractor_employees_count';
            } else if ($request->contract_type == 2) {
                $message = 'fetched_bilmaqta_contractor_employees_count';
            }

            // send success response with count
            return $this->getSuccessResponse(['all' => $all_count, 'employees' => $employees_count, 'ajirs' => $ajirs_count, 'militaries' => $militaries_count], trans('recruitment/recruitment.' . $message));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('general.fetched_fail'), $ex);
        }
    }


    /**
     * Get one employee list of records [ریکارد های قرار داد ها یک کارمند]
     * @param  request contains employee_id param
     */
    public function getEmployeeContracts(Request $request)
    {
        try {
            $employee_id = decrypt($request->employee_id);

            // get data from repository
            $data = $this->employeeContractRepository->getEmployeeContracts($employee_id);

            // render the datatable
            $rendered_contracts = View::make('components.pages.recruitment.employee_edit.contracts.contracts_datatable', ['data' => $data])->render();

            // send success response
            return $this->getSuccessResponse([$rendered_contracts], trans('general.fetched_success'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('general.fetched_fail'), $ex);
        }
    }

    /**
     * Get employee contracts rendered blade
     * @param request contains employee_id param
     */
    public function getEmployeeContractsTab(Request $request)
    {

        try {
            if (canDo('recruitment-contractor-emp_show_contractor')) {
                $employee_id = decrypt($request->employee_id);
                $data['employee_id'] = $employee_id;
                $data['records'] = $this->employeeContractRepository->getEmployeeContracts($employee_id);
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);

                // render the datatable
                $tab = View::make('components.pages.recruitment.employee_edit.contracts', ['data' => $data])->render();

                // send success response
                return $this->getSuccessResponse([$tab], trans('general.fetched_success'));
            }

            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.fetched_fail'), $ex);
            //throw $ex;
        }
    }

    /**
     * Get one record of employee contract [ریکارد قرار داد]
     * @param request contains contract_id param
     */
    public function getEmployeeContractById(Request $request)
    {
        try {
            // get the record from repository
            $contract = $this->employeeContractRepository->getEmployeeContractById($request->contract_id);

            if (!is_null($contract)) {
                return $this->getSuccessResponse($contract, trans('general.fetched_success'));
            }

            return $this->getErrorResponse(null, trans('general.not_found'));
        } catch (\Exception $ex) {
            return $this->getErrorResponse(null, trans('general.not_found'), $ex);
        }
    }

    /**
     * Create new contract record for employee
     * The request is validated in use EmployeeContractStoreRequest
     * @param  request contains employee_id and all the data param
     */
    public function store(EmployeeContractStoreRequest $request)
    {
        // dd($request);
        // dd('this is here');
        try {
            if (canDo('recruitment-contractor-emp_employee_registration')) {
                FacadesDB::beginTransaction();
                // employee
                $employee = $this->employeeRepository->getEmployeeById($request->employee_id);

                // 1. check if employee is not empty
                if (!is_null($employee)) {
                    // 2. check if employee's mawqif is asli or employee is barhal
                    if ($employee->employee_mawqif_id == 1 || $employee->current_status == 1) {
                        FacadesDB::rollback();
                        return $this->getErrorResponse(null, trans('recruitment/recruitment.barhal_employee_cannot_be_contractor'), 'barhal_employee_cannot_be_contractor');
                    }

                    // 2. check if employee is in service period
                    if ($employee->employee_mawqif_id == 2) {
                        FacadesDB::rollback();
                        return $this->getErrorResponse(null, trans('recruitment/recruitment.service_employee_cannot_be_contractor'), 'service_employee_cannot_be_contractor');
                    }

                    // 3. check if employee is in tahsil period
                    if ($employee->employee_mawqif_id == 3) {
                        FacadesDB::rollback();
                        return $this->getErrorResponse(null, trans('recruitment/recruitment.tahsili_employee_cannot_be_contractor'), 'tahsili_employee_cannot_be_contractor');
                    }

                    // 4. check if already has active contracts
                    $contracts = $this->employeeContractRepository->getEmployeeInProgressContractsByEmployeeId($employee->id);
                    if (!is_null($contracts) && count($contracts) > 0) {
                        FacadesDB::rollback();
                        return $this->getErrorResponse(null, trans('recruitment/recruitment.employee_already_has_active_contracts'), 'employee_already_has_active_contracts');
                    } else {


                        if ($request->contract_type == 1) {
                            // get nta_tashkil bast
                            $nta_tashkil_bast = $this->ntaTashkilatRepository->getNtaTashkil($request->contract_job_title);
                            if (!is_null($nta_tashkil_bast)) {
                                // check if bast is not occupied
                                if ($nta_tashkil_bast->status == 1) {
                                    $error = '<span id="name_error" class="my-error">' . trans('general.tashkil_bast_is_occupied') . '</span>';
                                    FacadesDB::rollback();
                                    return $this->getErrorResponse($error, 'development/dev.tashkil_bast_is_already_occupied', 'tashkil_bast_is_occupied');
                                }
                            }
                        }

                        $contract_data = [
                            'employee_id' => $employee->id,
                            'contract_type' =>  $request->contract_type,
                            'start_date' => $request->contract_start_date,
                            'end_date' => $request->contract_end_date,
                            'hokm_date' => $request->contract_hokm_date,
                            'hokm_number' => $request->contract_hokm_number,
                        ];

                        if ($request->contract_type == 1) {
                            $contract_data['nta_tashkil_id'] = $nta_tashkil_bast->id;
                            $contract_data['employee_type'] = $nta_tashkil_bast->employee_type;
                            $contract_data['job_title'] = $nta_tashkil_bast->title;
                            $contract_data['nta_grid'] = $nta_tashkil_bast->nta_grid_id;
                            $contract_data['nta_step'] = $nta_tashkil_bast->nta_step_id;
                        } else {
                            $contract_data['employee_type'] = $request->contract_employee_type;
                            $contract_data['job_title'] = $request->contract_job_title;
                        }

                        // contract department
                        $contract_parent_department = $this->departmentRepository->getDepartmentByid($request->contract_parent_department);
                        $contract_department = $this->departmentRepository->getDepartmentByid($request->contract_department);

                        // contract departments names
                        $contract_data['parent_department_id'] = $contract_parent_department->id;
                        $contract_data['parent_department_ps'] = $contract_parent_department->name_ps;
                        $contract_data['parent_department_dr'] = $contract_parent_department->name_dr;
                        $contract_data['parent_department_en'] = $contract_parent_department->name_en;

                        $contract_data['department_id'] = $contract_department->id;
                        $contract_data['department_ps'] = $contract_department->name_ps;
                        $contract_data['department_dr'] = $contract_department->name_dr;
                        $contract_data['department_en'] = $contract_department->name_en;

                        // 5. save contract
                        $saved_contract = $this->employeeContractRepository->store($contract_data);

                        if ($saved_contract != false) {
                            // 6. change employee to contractor
                            $employee->is_contract = 1;
                            $employee->save();

                            // 7. upload attachment
                            if ($request->hasFile('contract_hokm_attachment')) {
                                $url = $this->employeeAttachmentRepository->upload($request->file('contract_hokm_attachment'), $this->employeeAttachmentRepository->contract_hokm_attachment_type_id, $employee->id, $request->contract_department, $saved_contract->id);
                                $saved_contract->attachment_url = $url;
                                $saved_contract->save();
                            }

                            if ($request->contract_type == 1) {
                                // 8. change nta_tashkil bast status to occupied [status = 1]
                                $this->ntaTashkilatRepository->markBastOccupiedById($request->contract_job_title);
                            }

                            FacadesDB::commit();
                            return $this->getSuccessResponse(null, trans('general.created_success'));
                        }
                    }
                }
                FacadesDB::rollback();
                return $this->getErrorResponse(null, trans('general.created_fail'));
            }
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            FacadesDB::rollback();
            return $this->getErrorResponse(null, trans('general.created_fail'), $ex);
            //throw $ex;
        }
    }

    /**
     * Get one record of employee contract [ریکارد قرار داد]
     * @param  request contains contract id param
     */
    public function showEmployeeContractEditForm(Request $request, $id)
    {
        try {
            if (canDo('recruitment-contractor-emp_employee_edit')) {

                // get the record from repository
                $data['contract'] = $this->employeeContractRepository->getEmployeeContractById($id);
                

                // get all parent departments
                $data['parent_departments'] = $this->departmentRepository->getDepartmentsRendered('parent', null, 1, false);
                $data['parent_department'] = $this->departmentRepository->getDepartmentByid($data['contract']->parent_department_id);

                // get departments
                $data['departments'] = $this->departmentRepository->getDepartmentsRendered('child', $data['contract']->parent_department_id, 1, false);
                $data['department'] = $this->departmentRepository->getDepartmentByid($data['contract']->department_id);

                // get NTA tashkilat
                $data['nta_tashkil'] = $this->ntaTashkilatRepository->getNtaTashkil($data['contract']->nta_tashkil_id);

                $nta_tashkilat = $this->ntaTashkilatRepository->getFreeNtaBastsByDepartmentId($data['contract']->department_id);
                $nta_tashkilat->prepend($data['nta_tashkil']);
                $data['nta_tashkilat'] = $nta_tashkilat;

                // get NTA grid and step
                $data['nta_grid'] = NTAGrid::find($data['contract']->nta_grid);
                $data['nta_step'] = NTAStep::find($data['contract']->nta_step);

                // get employee types
                $data['employee_types'] = $this->employeeRepository->getEmployeeTypes();
                $data['employee_type'] = EmployeeType::find($data['contract']->employee_type_id);

                // render the component
                $rendered_contract = View::make('components.pages.recruitment.employee_edit.contracts.contract_edit_modal', ['data' => $data])->render();

                // send success response
                return $this->getSuccessResponse(['rendered_contract' => $rendered_contract, 'contract' => $data['contract']], trans('general.fetched_success'));
            }
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.fetched_fail'), $ex);
            //throw $ex;
        }
    }

    /**
     * Update contract record
     * The request is validated in use EmployeeContractUpdateRequest
     * @param  request contains contract_id and all the data param
     */
    public function update(EmployeeContractUpdateRequest $request)
    {
        try {
            if (canDo('recruitment-contractor-emp_employee_edit')) {
                // 1. employee
                $employee = $this->employeeRepository->getEmployeeById($request->employee_id);

                // 2. contract record
                $contract = $this->employeeContractRepository->getEmployeeContractById($request->contract_id);

                // 3. check if employee and contract are available
                if (!is_null($employee) && !is_null($contract)) {
                    $contract_data = [
                        'employee_id' => $employee->id,
                        'contract_id' => $request->contract_id,
                        'employee_type' => $request->contract_employee_type_edit,
                        'contract_type' =>  $request->contract_type_edit,
                        'job_title' => $request->contract_job_title_edit,
                        'start_date' => $request->contract_start_date_edit,
                        'end_date' => $request->contract_end_date_edit,
                        'hokm_date' => $request->contract_hokm_date_edit,
                        'hokm_number' => $request->contract_hokm_number_edit,
                        'nta_grid' => $request->contract_nta_grid_edit,
                        'nta_step' => $request->contract_nta_step_edit
                    ];

                    // contract department
                    $contract_parent_department = $this->departmentRepository->getDepartmentByid($request->contract_parent_department_edit);
                    $contract_department = $this->departmentRepository->getDepartmentByid($request->contract_department_edit);

                    // contract departments names
                    $contract_data['parent_department_id'] = $contract_parent_department->id;
                    $contract_data['parent_department_ps'] = $contract_parent_department->name_ps;
                    $contract_data['parent_department_dr'] = $contract_parent_department->name_dr;
                    $contract_data['parent_department_en'] = $contract_parent_department->name_en;

                    $contract_data['department_id'] = $contract_department->id;
                    $contract_data['department_ps'] = $contract_department->name_ps;
                    $contract_data['department_dr'] = $contract_department->name_dr;
                    $contract_data['department_en'] = $contract_department->name_en;

                    // 4. update contract
                    $updated = $this->employeeContractRepository->update($contract_data);

                    // updated successfully contract record
                    if ($updated != false) {
                        // 5. upload attachment
                        if ($request->hasFile('contract_hokm_attachment_edit')) {
                            // 6. delete the previous attachment
                            $this->employeeAttachmentRepository->delete($contract->attachment_url, $this->employeeAttachmentRepository->contract_hokm_attachment_type_id, $contract->id);

                            // 7. upload new attachment
                            $new_attachment_url = $this->employeeAttachmentRepository->upload($request->file('contract_hokm_attachment_edit'), $this->employeeAttachmentRepository->contract_hokm_attachment_type_id, $employee->id, $employee->department_id, $updated->id);
                            $updated->attachment_url = $new_attachment_url;
                            $updated->save();
                        }
                        return $this->getSuccessResponse(null, trans('general.updated_success'));
                    }
                }
                return $this->getErrorResponse(null, trans('general.updated_fail'));
            }
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.updated_fail'), $ex);
            //throw $ex;
        }
    }

    /**
     * Update contract record status
     * The request is validated in use EmployeeContractStatusUpdateRequest
     * @param request contains contract_id, reason params
     */
    public function updateStatus(EmployeeContractStatusUpdateRequest $request)
    {

        try {
            if (canDo('recruitment-contractor-emp_employee_edit')) {
                FacadesDB::beginTransaction();
                // get the contract record
                $contract = $this->employeeContractRepository->getEmployeeContractById($request->contract_id);

                // get the employee record
                $employee = $this->employeeRepository->getEmployeeById($request->employee_id);

                // check if contract is available
                if (!is_null($employee)) {
                    if (!is_null($contract)) {
                        // update status
                        $updated = $this->employeeContractRepository->updateStatus($request);

                        // send success response
                        if (isset($updated)) {
                            if ($request->contract_status_type == 2) { // suspended
                                $message = trans('recruitment/recruitment.employee_contract_suspended');
                            } else if ($request->contract_status_type == 3) { // cancelled
                                $message = trans('recruitment/recruitment.employee_contract_cancelled');
                            }
                            FacadesDB::commit();
                            return $this->getSuccessResponse(null, trans($message));
                        }
                    }
                    FacadesDB::rollback();
                    return $this->getErrorResponse(null, trans('general.not_found'));
                }
                FacadesDB::rollback();
                return $this->getErrorResponse(null, trans('general.employee_not_found'));
            }
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            FacadesDB::rollback();
            return $this->getErrorResponse(null, trans('recruitment/recruitment.employee_contract_record_status_update_failed'), $ex);
            //throw $ex;
        }
    }

    /**
     * Delete contract attachment
     * The request is validated in use EmployeeContractAttachmentDeleteRequest
     * @param request contains contract_id
     */
    public function deleteAttachment(EmployeeContractAttachmentDeleteRequest $request)
    {

        try {
            if (canDo('documents-emp_view_promotions_info')) {
                // get the contract record
                $contract = $this->employeeContractRepository->getEmployeeContractById($request->contract_id);

                if (!is_null($contract)) {
                    // delete the attachment [Physical file and the record in employee_attachment]
                    $deleted = $this->employeeAttachmentRepository->delete($contract->attachment_url, $this->employeeAttachmentRepository->contract_hokm_attachment_type_id, $contract->id);
                    if ($deleted == true) {
                        $contract->attachment_url = null;
                        $contract->save();
                        return $this->getSuccessResponse(null, trans('general.deleted_success'));
                    } else {
                        return $this->getErrorResponse(null, trans('general.deleted_fail'));
                    }
                }
                return $this->getErrorResponse(null, trans('general.not_found'));
            }
            return notAllowed([], $request);
            //code...
        } catch (\Exception $ex) {
            log::error($ex->getMessage());
            return $this->getErrorResponse(null, trans('general.deleted_fail'), $ex);
            //throw $ex;
        }
    }
}
